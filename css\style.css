
html, body {
    margin: 0;
    padding: 0;
    background-color: rgb(20, 20, 20);
    font-size: 1rem;
    font-family: 'Gill Sans', '<PERSON> MT', <PERSON><PERSON><PERSON>, 'Trebuchet MS', sans-serif, Helvetica, sans-serif;
    color: rgb(214, 214, 214);
    color: rgb(172, 172, 172);
    overflow-x: hidden;
    line-height: 1.4em;
    background-color: rgb(22, 22, 22);
}

html, body a {
    font-size: 1rem;
    color: rgb(214, 214, 214);
}
h1 {
    display: none;
}

a {
    font-size: 10rem;
}







/* === menu ======================================== */


header .menu-background {
    width: 100%;
    position: fixed;
    z-index: 10;
    transition-duration: 0.3s;
    background-color: rgb(16, 16, 16);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}
.at-top header .menu-background {
    background-color: rgba(0, 0, 0, 0.158);
    transition-duration: 0.3s;
    border-bottom: 1px solid grey;
}




header .menu {
    width: 100%;
    padding: 0px;
    margin: 0;
    /* max-width: 1400px; */
    margin: auto;
}

header .menu ul {
    float: right;
    display: flex;
    flex-wrap: wrap;
    list-style: none;
    padding: 0;
    margin: 0;
    column-gap: 1rem;
    padding-right: 1rem;
    /* margin-right: 1rem; */
}

header .menu ul li {
    position: relative;
    /* padding: 0.5rem; */
    padding-right: 0rem;
    padding-left: 0rem;
    border-bottom: 4px solid transparent;
    transition-duration: 0.5s;
    margin: 0rem;
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
    padding-top: 0.4rem;
    padding-bottom: 0.4rem;
    transition-duration: 0.3s;
}

header .menu ul li a {
    color: rgb(196, 196, 196);
    padding: 0.5rem;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    text-decoration: none;
    /* padding-top: 2rem; */
    text-transform: uppercase;
    font-size: 0.875rem;
    font-weight: 700;
    border-radius: 1rem;
    /* letter-spacing: 0.1rem; */
    font-family: "Roboto","Helvetica","Arial",sans-serif;
    transition-duration: 0.3s;
}


header .menu ul li:hover a, header .menu .home-button:hover {
    color: white;
    transition-duration: 0.3s;
}

header .menu ul li.scrolled{
    /* border-bottom: 3px solid rgb(73, 73, 73); */
    transition-duration: 0.3s;
}

header .menu ul li:hover a {
    background-color: rgba(128, 128, 128, 0.432);
}

/* contact me */
header .menu ul li:last-child a {
    background-color: rgb(255, 255, 255);
    transition-duration: 0.3s;
    color: rgb(85, 85, 85);
}

header .menu ul li:last-child:hover a {
    background-color: rgb(194, 194, 194);
    transition-duration: 0.3s;
}


header .menu .home-button {
    position: absolute;
    /* font-family: Tahoma, Verdana, Segoe, sans-serif; */
    text-decoration: none;
    padding: 0.75rem;
    font-size: 1.3rem;
    transition-duration: 0.5s;
    color: rgb(196, 196, 196);
    font-weight: bold;
    padding-top: 1rem;
    margin-left: 3rem;
    font-family: "Roboto","Helvetica","Arial",sans-serif;
}


/* more button */
header .menu .more {
    display: none;
    font-size: 0;
    width: 4rem;
    min-height: 4rem;
    position: absolute;
    background-color: transparent;
    right: 0;
    border: none;
    margin: 0;
    padding: 0;
}

header .menu .more::before {
    position: absolute;
    font-family: FontAwesome;
    content: "\f0c9";
    font-size: 2rem;
    color: white;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    padding-top: 1.0rem;
}



header .menu .more:hover::before {
    color: rgb(206, 206, 206);
}





header .background {
    position: relative;
    z-index: 1;
    overflow: hidden;
    height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    background-color: rgb(24, 24, 24);
}

header #tsparticles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
    pointer-events: none;
}

header .sub {
    position: relative;
    z-index: 2;
    display: flex;
    flex-direction: column;
    /* row-gap: 3rem; */
}


header .profile-pic {
    width: 11rem;
    height: 11rem;
    border-radius: 10rem;
    overflow: hidden;
    margin: auto;
}
header .profile-pic img {
    width: 100%;
    height: 100%;
    height: auto;
    display: block;
    object-fit: cover;
    transition-duration: 0.5s;
}
header .background .sub .profile-pic img:hover {
    transform: scale(1.07);
    transition-duration: 0.5s;
}






header .modal {
    display: none;
    position: fixed;
    z-index: 999;
    inset: 0;
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(8px);
    justify-content: center;
    align-items: center;
}

header .modal-content img {
    z-index: 10000;
    max-width: 40%;
    max-height: 80%;
    border-radius: 4rem;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.6);
    transition: transform 0.3s;
}

header .modal.show {
    display: flex;
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to   { opacity: 1; }
}







header .background .sub .name {
    font-size: 4.5em;
    z-index: 1;
    color: white;
    padding-bottom: 1rem;
    text-align: center;
    font-family: "Nunito","Roboto","Helvetica","Arial",sans-serif;
    margin-top: 4rem;
    margin-bottom: 3rem;
    letter-spacing: 0.2rem;
}

header .background .sub .major {
    font-size: 1.9rem;
    z-index: 1;
    /* top: 19rem; */
    color: rgb(170, 170, 170);
    text-align: center;
    /* margin-bottom: 3rem; */
    letter-spacing: 0.2rem;
    margin-bottom: 3rem;
    font-family: "Nunito","Roboto","Helvetica","Arial",sans-serif;

}





/*  downlaod my full resume buttom */
header .background .sub .download-cv {
    margin-top: 2rem;
    text-align: center;
}
header .background .sub .download-link {
    text-decoration: none;
    border: 1px solid grey;
    padding: 1rem;
    font-size: 1rem;
    border-radius: 1rem;
    transition-duration: 0.3s;
    background-color: rgb(22, 22, 22);
    position: relative;

}
header .background .sub .download-link:hover {
    background-color: rgb(31, 31, 31);
    transition-duration: 0.3s;
}


/* social networks */
header .social-networks {
    text-align: center;
    margin: auto;
    margin-top: 2rem;
}


header .social-networks ul {
    list-style-type: none;
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
    padding: 0;
}

header .social-networks ul li a {
    font-size: 1.5rem;
}

header .social-networks ul li a:hover {
    font-size: 1.5rem;
    color: white;
}





/* === about =========================================== */

.about, .education, .skills, .experience, .awards, .portfolio {
    max-width: 1100px;
    padding-left: 3rem;
    padding-right: 3rem;
    margin: auto;
}

h2 {
    text-align: center;
    font-size: 2em;
    font-weight: bold;
    margin-top: 1rem;
    margin-bottom: 0.7rem;
    position: relative;
    margin-bottom: 2rem;
    color: white;

}



.about::after, .education::after, .skills::after, .experience::after, .awards::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 2rem;
    right: 2rem;
    height: 1px;
    border-bottom: 1px solid rgb(97, 97, 97);
}

.none-heading {
    display: none;
}



.about {
    position: relative;
    padding-bottom: 4rem;
    padding-top: 2rem;
    max-width: 1300px;
}

.about-info-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 5rem;
  /* padding: 2rem; */
  color: #eee;
  font-family: 'Segoe UI', sans-serif;
}

.about-me, .personal-info {
  flex: 1 1 400px;
  min-width: 300px;
}

.about-me h2, .personal-info h2 {
  font-size: 1.8rem;
  margin-bottom: 3rem;
  color: #fff;
  letter-spacing: 1px;
}


.about-me p {
  margin-bottom: 1rem;
  line-height: 1.6;
  color: #ccc;
}


.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.2rem 2rem;
  margin-top: 1rem;
}

.info-item {
  font-size: 1rem;
}

.info-item strong {
  display: block;
  color: #aaa;
  margin-bottom: 0.2rem;
  font-weight: 400;
}

.info-item span {
  font-weight: 600;
  color: #fff;
}

@media (max-width: 768px) {
  .info-grid {
    grid-template-columns: 1fr;
  }
}






/* === Education ======================================== */



.education   {
    text-align: center;
    padding-top: 4rem;
    position: relative;
}
.education .sub {
    padding-bottom: 4rem;
    margin: auto;
    position: relative;
}


.education .background {
    position: absolute;
    width: 100%;
    top: -2rem;
    bottom: 2rem;
    filter: blur(0px);
    background-attachment: fixed;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
}


.education .content {
    display: flex;
    flex-wrap: wrap;
    gap: 4rem;
    position: relative;
    margin-top: 2rem;
    padding-bottom: 3rem;
}

.education section {
    flex: 1 1 20%;
    padding: 1.5rem;
    border-radius: 2rem;
    text-align: center;
    border: 1px solid grey;
    transition-duration: 0.5s;
    background-color: rgb(14, 14, 14);

}
.education section:hover {
    transform: scale(1.010);
    transition-duration: 0.5s;
}


.education .main-info {
    display: flex;
    text-align: right;
    justify-content: center;
    padding: 0;
    margin: 0;
    gap: 1rem;
}

.education img {
    width: 4rem;
    height: 4rem;
    border-radius: 3rem;
    object-fit: contain;
}

.education .img2 {
    display: flex;
    justify-content: center;
    flex-direction: column;
    padding-top: 0.5rem;
}

.education .sub2 {
    text-align: left;
}


.education .content a {     /* school name */
    text-decoration: none;
    font-size: 1.5em;
    font-weight: bold;
    transition-duration: 0.4s;
}
.education .content a:hover {
    color: rgb(86, 118, 134);
    letter-spacing: 1.5px;
    transition-duration: 0.4s;
}


.education .content section .date {      /* date */
    color: darkgrey;
    font-size: 1.3em;
    font-weight: bold;
    color: rgb(136, 136, 136);
}
.education .content p {          /* school dexcription */
    font-size: 1em;
    text-align: justify;

}

.education h3 {
    margin-bottom: 0.3rem;
}












/* === Experience Timeline ======================== */



.experience {
    position: relative;
    margin: auto;
    padding-top: 3rem;
    padding-bottom: 1rem;
}

.timeline {
    padding: 0;
    margin: 0;
    position: relative;
}

.timeline::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: calc(50% - 1px);
    width: 2px;
    top: 3rem;
    bottom: 3rem;
    background-color: rgb(75, 75, 75);
}

.timeline .item {
    display: flex;
    align-items: center;
    margin-bottom: 4rem;
    position: relative;
    margin: 0;
    margin-bottom: 2rem;
}

.timeline .item::after {
    content: "";
    position: absolute;
    width: 1rem;
    height: 1rem;
    z-index: 2;
    background-color: rgb(50, 63, 119);
    left: calc(50% - 0.5rem);
    top: 50%;
    border-radius: 1rem;
}

.timeline .item section {
    flex: 1 1 50%;
    border-radius: 2rem;
    padding: 2rem;
}


/* main info */
.timeline .main-info {
    padding: 0;
    margin: 0;
    justify-content: right;
    display: flex;
}

.timeline .main-info .sub {
    display: flex;
    padding: 0;
    margin: 0;
    align-items: center;
    text-decoration: none;
    transition-duration: 0.3s;
}

.timeline .main-info .sub:hover {
    transform: scale(1.02);
    transition-duration: 0.3s;
}

.timeline .company-info {
    margin-right: 1rem;
    text-align: right;
}

.timeline .company-logo {
    width: 4rem;
    height: 4rem;
    border-radius: 1rem;
    object-fit: contain;
    margin: 0;
    padding: 0;
    background-color: grey;
}


.timeline .company-logo img {
    width: 4rem;
    height: 4rem;
    border-radius: 1rem;
    margin: 0;
    padding: 0;
    background-color: white;
}

.timeline .main-info h3 {
    font-size: 1.6rem;
    color: white;
    margin: 0;
    margin-bottom: 0.5em;
}



/* basic info */
.timeline .basic-info h3 {
    color: white;
    padding: 0;
    margin: 0;
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
}

.timeline .basic-info {
    padding: 0;
    margin: 0;
    justify-content: right;
    display: flex;
    justify-content: left;
}

.timeline .basic-info p {
    padding: 0;
    margin: 0;
}







/* invert even item */

#even .main-info {
  order: 2;
}
#even .basic-info {
  order: 1;
}

#even .company-logo {
  order: 1;
}
#even .company-info {
  order: 2;
}

#even .main-info .company-info {
    margin-left: 1rem;
    text-align: left;
}

#even .main-info {
    justify-content: left;
}

#even .basic-info {
    justify-content: right;
}

#even .basic-info .sub {
    text-align: right;
}







/* === skills ======================== */

.skills {
    padding-top: 4rem;
    position: relative;
    padding-bottom: 4rem;
}



.skills-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
  /* padding: 2rem; */
  /* background-color: #0e0e0e; */
  color: #fff;
  font-family: sans-serif;
}

.skill {
  background: #121212;
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 0 10px rgba(255,255,255,0.05);
}

.skills img {
    filter: brightness(0) invert(1);
    height: 1.2rem;
}

.skill-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
}

.skill-header i {
  margin-right: 0.5rem;
  color: #fff;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #2a2a2a;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.8rem;
}

.progress {
  height: 100%;
  background: linear-gradient(90deg, #8091af, #405470);
  border-radius: 4px;
}

.skill p {
  font-size: 0.9rem;
  color: #ccc;
}





/* === contact me ===================================== */

.contact {
    width: 100%;
    padding: 4rem 0;
    margin: 0;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, rgb(14, 14, 14) 0%, rgb(25, 25, 35) 50%, rgb(14, 14, 14) 100%);
    overflow: hidden;
}

.contact-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.1) 0%, transparent 50%);
    z-index: 1;
}

.contact-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(2px 2px at 20px 30px, rgba(255, 255, 255, 0.1), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(255, 255, 255, 0.05), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(255, 255, 255, 0.08), transparent);
    background-repeat: repeat;
    background-size: 100px 100px;
    animation: float 20s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.contact .sub-contact {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    padding: 2rem;
    max-width: 1200px;
    margin: auto;
    z-index: 2;
    gap: 3rem;
}

.contact .con {
    display: none;
}

.contact h2 {
    padding-top: 2rem;
    text-align: left;
}


.contact .sub-contact section {
    position: relative;
    flex: 1 1 45%;
    min-width: 300px;
}

/* --- contact header -------------------------------- */

.contact-header {
    text-align: center;
    margin-bottom: 3rem;
}

.contact-header h2 {
    font-size: 2.5rem;
    background: linear-gradient(45deg, #fff, #a855f7, #3b82f6);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 1rem;
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from { filter: drop-shadow(0 0 5px rgba(168, 85, 247, 0.3)); }
    to { filter: drop-shadow(0 0 20px rgba(168, 85, 247, 0.6)); }
}

.contact-subtitle {
    color: rgb(180, 180, 180);
    font-size: 1.1rem;
    line-height: 1.6;
    max-width: 400px;
    margin: 0 auto;
}

/* --- contact cards -------------------------------- */

.contact-cards {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    margin-top: 2rem;
}

.contact-card {
    display: flex;
    align-items: center;
    padding: 1.5rem;
    background: linear-gradient(145deg, rgba(30, 30, 40, 0.8), rgba(20, 20, 30, 0.9));
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.contact-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.contact-card:hover::before {
    left: 100%;
}

.contact-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(168, 85, 247, 0.2);
    border-color: rgba(168, 85, 247, 0.3);
}

.contact-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, #a855f7, #3b82f6);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1.5rem;
    font-size: 1.5rem;
    color: white;
    box-shadow: 0 5px 15px rgba(168, 85, 247, 0.3);
    transition: all 0.3s ease;
}

.contact-card:hover .contact-icon {
    transform: scale(1.1) rotate(10deg);
    box-shadow: 0 8px 25px rgba(168, 85, 247, 0.5);
}

.contact-info h3 {
    color: white;
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.contact-info p {
    color: rgb(180, 180, 180);
    font-size: 1rem;
    margin: 0;
    transition: color 0.3s ease;
}

.contact-card:hover .contact-info p {
    color: rgb(220, 220, 220);
}



/* --- form -------------------------------------- */

.form-container {
    background: linear-gradient(145deg, rgba(25, 25, 35, 0.9), rgba(15, 15, 25, 0.95));
    border-radius: 20px;
    padding: 2.5rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    position: relative;
    overflow: hidden;
}

.form-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #a855f7, #3b82f6, #a855f7);
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

.form-header {
    text-align: center;
    margin-bottom: 2rem;
}

.form-header h2 {
    font-size: 2rem;
    color: white;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.form-header p {
    color: rgb(180, 180, 180);
    font-size: 1rem;
    line-height: 1.6;
}

.contact form {
    display: grid;
    gap: 1.5rem;
}

.contact form input, .contact form textarea {
    padding: 1rem 1.2rem;
    border: 2px solid rgba(255, 255, 255, 0.1);
    background: rgba(40, 40, 50, 0.8);
    color: white;
    font-size: 1rem;
    border-radius: 12px;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
    position: relative;
}

.contact form input:focus, .contact form textarea:focus {
    outline: none;
    border-color: #a855f7;
    box-shadow: 0 0 20px rgba(168, 85, 247, 0.3);
    background: rgba(50, 50, 60, 0.9);
    transform: translateY(-2px);
}

.contact form input::placeholder, .contact form textarea::placeholder {
    color: rgba(180, 180, 180, 0.7);
    transition: color 0.3s ease;
}

.contact form input:focus::placeholder, .contact form textarea:focus::placeholder {
    color: rgba(200, 200, 200, 0.9);
}
/* .contact form input:not(:placeholder-shown) {
    background-color: transparent;
}
*/

.contact form textarea {
    resize: vertical;
    min-height: 120px;
    font-family: inherit;
}

.contact form label {
    font-size: 1rem;
    margin-bottom: 0.5rem;
    color: rgb(220, 220, 220);
    font-weight: 500;
    display: block;
    position: relative;
}

.contact form label::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(45deg, #a855f7, #3b82f6);
    transition: width 0.3s ease;
}

.contact form input:focus + label::after,
.contact form textarea:focus + label::after {
    width: 100%;
}


.contact form .name fieldset input {
    width: 100%;
}

.contact form .name {
    display: flex;
    flex-wrap: nowrap;
    padding: 0;
    border: none;
    column-gap: 3rem;
    padding-right: 1rem;
}
.contact form .name fieldset {
    flex: 1 1 40%;
    padding: 0;
    border: none;
}

.contact form .name fieldset input {
    margin-top: 0.5rem;
}



/* submit button */
.contact form #submit {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.8rem;
    padding: 1.2rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    border: none;
    border-radius: 15px;
    background: linear-gradient(45deg, #a855f7, #3b82f6);
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(168, 85, 247, 0.3);
    margin-top: 1rem;
}

.contact form #submit::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.contact form #submit:hover::before {
    left: 100%;
}

.contact form #submit:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(168, 85, 247, 0.5);
    background: linear-gradient(45deg, #9333ea, #2563eb);
}

.contact form #submit:active {
    transform: translateY(-1px);
}

.contact form #submit i {
    transition: transform 0.3s ease;
}

.contact form #submit:hover i {
    transform: translateX(5px);
}


/* show text after submit */
.contact form .submit-info {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.8rem;
    opacity: 0;
    margin: 1rem auto 0;
    padding: 1rem 1.5rem;
    background: linear-gradient(45deg, rgba(34, 197, 94, 0.2), rgba(16, 185, 129, 0.2));
    border: 1px solid rgba(34, 197, 94, 0.3);
    border-radius: 12px;
    color: rgb(34, 197, 94);
    font-weight: 500;
    transform: translateY(-1rem) scale(0.9);
    transition: all 0.4s ease;
    backdrop-filter: blur(5px);
}

.contact form .submit-info i {
    font-size: 1.2rem;
    animation: checkmark 0.6s ease-in-out;
}

@keyframes checkmark {
    0% { transform: scale(0) rotate(0deg); }
    50% { transform: scale(1.2) rotate(180deg); }
    100% { transform: scale(1) rotate(360deg); }
}

.contact form.open .submit-info {
    opacity: 1;
    transform: translateY(0) scale(1);
}













/* === Awards ========================================== */


.awards {
    position: relative;
    margin: auto;
    padding-top: 4rem;
    padding-bottom: 2rem;
}



.awards ul li {
    list-style: none;
    position: relative;
    margin-left: 1rem;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
}

.awards ul li::before {
    position: absolute;
    font-family: FontAwesome;
    content: "\f091";
    color: yellow;
    left: -2rem;
    font-size: 1em;
}


/* === Portfolio ============================================== */


.portfolio {
    margin: auto;
    padding-top: 3rem;
    margin-bottom: 6rem;
    max-width: 1100px;
}
.portfolio .projects {
    margin-top: 3rem;
  /* display: grid; */
  /* grid-template-columns: repeat(2, 1fr); */
  /* gap: 2rem; */
}

.portfolio h3 {
    font-size: 1.5em;
    position: relative;
    color: white;
    font-weight: bold;
    margin: 0;
}

.portfolio section .content {
    display: flex;
    flex-wrap: wrap;
    position: relative;
    gap: 3rem;
}


.portfolio .projects section {
    border: 1px solid rgb(90, 90, 90);
    border-radius: 2rem;
    padding: 2rem;
    margin-bottom: 2rem;
    transition-duration: 0.5s;
    background-color: rgb(14, 14, 14);
}


.portfolio .content .content-2, .portfolio .content .img2 {
    flex: 1 20%;
}

.portfolio .content .img2 {
    display: flex;
    align-items: center;
    text-align: center;
    justify-content: center;
    max-width: 22rem;
    position: relative;
}






.collapsible-text {
  overflow: hidden;
  max-height: 4.5em;
  transition: max-height 0.5s ease;
}

.toggle-button {
  margin-top: 10px;
  background-color: transparent;
  border: none;
  color: #007bff;
  font-size: 1em;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 0.5em;
}

.arrow {
  transition: transform 0.3s ease;
}

.expanded + .toggle-button .arrow {
  transform: rotate(180deg); /* Pokud chceš rotaci místo změny znaku */
}
.text-container {
  position: relative;
}

.collapsible-text {
  overflow: hidden;
  max-height: 6.5em; /* přibližně 3 řádky */
  transition: max-height 0.5s ease;

  /* zde je klíčové maskování */
  -webkit-mask-image: linear-gradient(to bottom, black 0%, transparent 100%);
  mask-image: linear-gradient(to bottom, black 0%, transparent 100%);
}

.collapsible-text.expanded {
  max-height: 1000px;
  -webkit-mask-image: none;
  mask-image: none;
}




.portfolio .img3 {
    position: relative;
    height: 15rem;
    width: 100%;
    overflow: hidden;
    border-radius: 2rem;
    position: relative;
    /* box-shadow: 5px 5px 5px rgb(26, 26, 26); */
}


.portfolio .projects section:hover {
    transform: scale(1.005);
    transition-duration: 0.5s;
}

.portfolio .content .img2 img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition-duration: 0.3s;
    z-index: 0;
}

.portfolio .content .img2:hover img {
    transform: scale(1.05);
    transition-duration: 0.3s;
    filter: blur(1px) brightness(70%);;
}



.portfolio .content .img2 .img-label {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s;
    z-index: 1;
    display: block;
    text-decoration: none;
    border: 1px solid grey;
    padding: 0.7rem;
    font-size: 1rem;
    border-radius: 0.78em;
    transition-duration: 0.3s;
    background-color: rgb(22, 22, 22);
}

.portfolio .content .img2:hover .img-label {
    opacity: 1;
    pointer-events: auto;



}

.portfolio .projects section .content-2 {
    text-align: justify;
    margin-bottom: 1.3rem;
    color: rgb(172, 172, 172);
}


.portfolio .content-2 {
    display: flex;
    justify-content: center;
    flex-direction: column;
}


.portfolio .checkout{
    text-decoration: none;
    border: 1px solid grey;
    padding: 0.7rem;
    font-size: 1rem;
    border-radius: 0.78em;
    transition-duration: 0.3s;
    background-color: rgb(22, 22, 22);
    position: relative;
    /* padding-right: 2.7rem; */
}

.portfolio .toggle-button {
    color: rgb(199, 199, 199);
    font-size: 1rem;
    margin-left: 1rem;
}

.portfolio .checkout:hover {
    background-color: rgb(31, 31, 31);
    transition-duration: 0.3s;
}



.portfolio .content p {
    margin-bottom: 2rem;
    text-align: justify;
}

.portfolio .private a {
    color: grey;
}
.portfolio .private a:hover {
    background-color: rgb(22, 22, 22);
}








/* === footer ============================================== */

footer {
    position: relative;
    /* width: 100%; */
    display: flex;
    flex-wrap: wrap;
    max-width: 1100px;
    padding: 0;
    margin: 0;
    padding: 0;
    padding: 2rem;
    row-gap: 2rem;
    /* background-color: yellow; */
    margin: auto;
}

footer div {
    position: relative;
    flex: 1 1 10%;
    text-align: center;
    padding-bottom: 0rem;
    flex-direction: column;
    padding: 0;
    margin: 0;
}



/* downlaod cv button */
footer .download {
    padding: 0;
    margin: 0;
    display: flex;
    align-items: center;
    text-align: center;
    justify-content: center;
    /* background-color: red; */
}
footer .download a {
    position: relative;
    text-decoration: none;
    border: 1px solid grey;
    padding: 1rem;
    font-size: 1rem;
    border-radius: 1rem;
    transition-duration: 0.3s;
    background-color: rgb(22, 22, 22);

}
footer .download a:hover {
    background-color: rgb(31, 31, 31);
    transition-duration: 0.3s;
}







/* social networks */
footer .social-networks {
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: 0;
    margin: 0;
    row-gap: 0;
    padding-top: 1rem;
    /* background-color: red; */


}
footer .social-networks ul {
    list-style-type: none;
    display: flex;
    flex-wrap: wrap;
    gap: 1.2rem;
    padding: 0;
    margin: 0;
}
footer .social-networks ul li a {
    font-size: 1.4rem;
}
footer .social-networks ul li a:hover {
    color: white;
}

footer .social-networks p {
    margin-top: 0.7rem;
}








/* created by */
.created-by {
    position: relative;
    border-top: 1px solid grey;
    padding: 0;
    margin: 0;
    background-color: rgb(19, 19, 19);
}

.created-by .sub2 {
    position: relative;
    max-width: 1000px;
    /* padding-left: 3rem; */
    padding: 0;
    margin: 0;
    margin: auto;
    display: flex;
    justify-content: space-between; /* rozdělí obsah ke krajům */
    align-items: center;            /* zarovná vertikálně (volitelně) */
    padding-left: 2rem;
    padding-right: 2rem;
}


.website-link {
    position: relative;
    color: rgb(138, 138, 255);
    text-decoration: none;
}
















/* === scroll-up button ===================================== */

.scroll-up {
    position: fixed;
    width: 3.5rem;
    height: 3.5rem;
    right: 1rem;
    bottom: 1rem;
    background-color: rgba(155, 155, 155, 0.349);
    border-radius: 2rem;
    color: white;
    transition-duration: 0.3s;
    z-index: 11;
    border: none;
    display: block;
    color: grey;
}

.scroll-up::after {
    font-family: FontAwesome;
    content: "\f062";
    font-size: 1.5rem;
}

.scroll-up:hover {
    background-color: rgb(119, 119, 119);
    transition-duration: 0.3s;
    color: white;
}



.at-top .scroll-up {
    display: none;
}













/* === media ========================================== */


@media only screen and (max-width: 1200px) {
    header .background .sub .name {
        font-size: 2.6rem;
    }
    header .background .sub .major {
        font-size: 1.5em;
    }
}

@media only screen and (max-width: 1000px) {

    .about, .education, .skills, .experience, .awards, .portfolio {
        padding-left: 2rem;
        padding-right: 2rem;
    }

    /* --- menu ------------------------------------------- */

    header .menu {
        min-height: 2.5rem;
        font-size: 1rem;
    }
    header .menu ul li a {
        font-size: 1rem;
    }
    header .menu .home-button {
        font-size: 1.7rem;
    }

    header .menu-background {
        height: 3.8rem;
    }

    header .menu li {
        display: none;
    }

    header .menu ul li.scrolled{
        display: block;
        right: 5rem;
        display: none;
    }
    header .menu .more {
        display: block;
    }

    /* drop down menu */
    header .menu[data-state="rolled-out"] .home-button {
        transform: translateX(-500px);
        transition-duration: 0.4s;
    }
    header .menu[data-state="rolled-out"] .more::before {
        content: "\f00d";
    }

    header .menu[data-state="rolled-out"] ul {
        animation: roll-out1 0.7s ease forwards;
    }
    header .menu[data-state="rolled-out"] ul li {
        left: 0;
        display: block;
        padding-left: 2rem;
        border-bottom: 1px solid rgb(59, 59, 59);
        width: 100%;
    }




    /* --- about --------------------------------- */
    .about h2 {
        margin-top: 8rem;
    }
    .about a {
        left: 2rem;
        right: 3rem;
    }


    /* --- education ------------------------------------ */
    .education section {
        flex: 1 1 100%;  /* pod sebou */
    }



    /* --- skills ------------------------------------ */

    .skills-grid {
    grid-template-columns: 1fr; /* pouze jeden sloupec */
  }



    /* --- portfolio ---------------------------------- */
    .portfolio .content .content-2, .portfolio .content .img2 {
        flex: 1 100%;
    }
    .portfolio .content .img2 {
        margin: auto;
    }
    .portfolio .projects .content .content-2 {
        text-align: center;
    }

    .portfolio .projects section:nth-of-type(2) .content .content-2 {
        order: -1;
    }


    /* --- contact -------------------------- */
    .contact {
        padding: 2rem 0;
    }

    .contact .sub-contact {
        flex-direction: column;
        padding: 1rem;
        gap: 2rem;
    }

    .contact section {
        position: relative;
        z-index: 2;
        flex: 1 1 100%;
    }

    .contact-cards {
        gap: 1rem;
    }

    .contact-card {
        padding: 1.2rem;
    }

    .contact-icon {
        width: 50px;
        height: 50px;
        margin-right: 1rem;
        font-size: 1.3rem;
    }

    .form-container {
        padding: 1.5rem;
    }

    .contact form .name {
        flex-direction: column;
        gap: 0;
    }

    .contact-header h2 {
        font-size: 2rem;
    }

    .form-header h2 {
        font-size: 1.8rem;
    }



    /* --- Footer --------------------------------------- */
    footer div {
        flex: 1 1 100%;
    }



}


@keyframes roll-out1 {
    0% {
        transform: translateY(-1000px);
        height: 0;
    }
    20% {
        float: left;
        display: grid;
    }
    100% {
        background-color: black;
        transform: translate(0px, 0px);
        display: grid;
        float: left;
        width: 100%;
        border-bottom: 2px solid white;
    }
}

